<!DOCTYPE html>
<html lang="en" class="dark">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>AI Companion Setup</title>
<script defer src="/static/app.js"></script>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
<style>
canvas {
width: 100%;
max-width: 300px;
height: 50px;
background-color: #374151;
border-radius: 0.375rem;
}
.spinner {
border: 4px solid rgba(255, 255, 255, 0.3);
border-radius: 50%;
border-top: 4px solid #4ade80;
width: 30px;
height: 30px;
animation: spin 1s linear infinite;
margin: 0 auto;
display: none;
}
@keyframes spin {
 0% { transform: rotate(0deg); }
 100% { transform: rotate(360deg); }
}
.reference-section {
  border: 1px solid #4b5563;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}
.reference-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}
</style>
</head>
<body class="bg-gray-900 text-white">
<div class="min-h-screen flex items-center justify-center">
<div class="bg-gray-800 p-8 rounded-lg shadow-xl w-full max-w-3xl">
<h1 class="text-2xl font-bold mb-4 text-indigo-400">AI Companion Setup</h1>
<a href="/crud" class="absolute top-4 right-4 bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded">
 Open Conversation DB
</a>
<div class="grid grid-cols-1 gap-4">
<label>System Prompt:
<textarea id="systemPrompt" class="w-full p-2 bg-gray-700 rounded mt-1">You are a friendly AI.</textarea>
</label>
<label>Model Path:
<input type="text" id="modelPath" class="w-full p-2 bg-gray-700 rounded mt-1" value="./finetuned_model">
</label>
<label>LLM Path (GGUF):
<input type="text" id="llmPath" class="w-full p-2 bg-gray-700 rounded mt-1" value="./models/llama3-8b-instruct.gguf">
</label>

<!-- Primary Reference (Required) -->
<div class="reference-section">
  <div class="reference-heading">
    <h3 class="text-lg font-medium text-indigo-300">Primary Reference Audio (Required)</h3>
  </div>
  <label>Reference Audio:
  <input type="text" id="referenceAudio" class="w-full p-2 bg-gray-700 rounded mt-1" value="./reference.wav">
  </label>
  <label>Reference Text:
  <input type="text" id="referenceText" class="w-full p-2 bg-gray-700 rounded mt-1" value="Hi, how can I help?">
  </label>
</div>

<!-- Second Reference (Optional) -->
<div class="reference-section">
  <div class="reference-heading">
    <h3 class="text-lg font-medium text-indigo-300">Secondary Reference (Optional)</h3>
    <span class="text-xs text-gray-400">For better voice quality</span>
  </div>
  <label>Reference Audio 2:
  <input type="text" id="referenceAudio2" class="w-full p-2 bg-gray-700 rounded mt-1" value="">
  </label>
  <label>Reference Text 2:
  <input type="text" id="referenceText2" class="w-full p-2 bg-gray-700 rounded mt-1" value="">
  </label>
</div>

<!-- Third Reference (Optional) -->
<div class="reference-section">
  <div class="reference-heading">
    <h3 class="text-lg font-medium text-indigo-300">Tertiary Reference (Optional)</h3>
    <span class="text-xs text-gray-400">For even better voice quality</span>
  </div>
  <label>Reference Audio 3:
  <input type="text" id="referenceAudio3" class="w-full p-2 bg-gray-700 rounded mt-1" value="">
  </label>
  <label>Reference Text 3:
  <input type="text" id="referenceText3" class="w-full p-2 bg-gray-700 rounded mt-1" value="">
  </label>
</div>

<label>Microphone:
<select id="micSelect" class="w-full p-2 bg-gray-700 rounded mt-1"></select>
</label>
<div>
<canvas id="micCanvas" width="300" height="50"></canvas>
<button id="testMicBtn" class="bg-indigo-600 hover:bg-indigo-500 px-4 py-2 rounded mt-2">Test Mic</button>
</div>
<label>Headset / Output:
<select id="outputSelect" class="w-full p-2 bg-gray-700 rounded mt-1"></select>
</label>
<div>
<canvas id="outputCanvas" width="300" height="50"></canvas>
<button id="testOutputBtn" class="bg-indigo-600 hover:bg-indigo-500 px-4 py-2 rounded mt-2">Test Output</button>
</div>
<div class="flex flex-col items-center justify-center mt-4">
<button id="saveAndStart" class="bg-green-600 hover:bg-green-500 px-4 py-2 rounded w-full">Start Companion</button>
<div id="loadingContainer" class="mt-4 text-center hidden">
<div class="spinner" id="loadingSpinner"></div>
<p id="loadingText" class="mt-2 text-indigo-300">Initializing models, please wait...</p>
</div>
</div>
</div>
</div>
</div>
</body>
</html>